/**
 * CompanyAutoComplete组件调试工具
 */

import { getCompanyAutoComplete } from '@/api/basicinfo/company';

export interface DebugResult {
  success: boolean;
  message: string;
  data?: any;
  error?: any;
}

/**
 * 测试API连接
 */
export const testAPIConnection = async (): Promise<DebugResult> => {
  try {
    console.log('🔍 开始测试API连接...');
    
    const response = await getCompanyAutoComplete('', 10);
    
    if (response) {
      console.log('✅ API连接成功，响应:', response);
      
      if (response.success) {
        return {
          success: true,
          message: `API连接正常，返回${response.result?.length || 0}条数据`,
          data: response
        };
      } else {
        return {
          success: false,
          message: `API返回失败状态: ${response.message || '未知错误'}`,
          data: response
        };
      }
    } else {
      return {
        success: false,
        message: 'API返回空响应',
        data: response
      };
    }
  } catch (error) {
    console.error('❌ API连接失败:', error);
    return {
      success: false,
      message: `API连接失败: ${error.message || '未知错误'}`,
      error: error
    };
  }
};

/**
 * 测试搜索功能
 */
export const testSearchFunction = async (keyword: string): Promise<DebugResult> => {
  try {
    console.log(`🔍 开始测试搜索功能，关键词: "${keyword}"`);
    
    const response = await getCompanyAutoComplete(keyword, 50);
    
    if (response && response.success) {
      const resultCount = response.result?.length || 0;
      console.log(`✅ 搜索成功，找到${resultCount}条结果`);
      
      return {
        success: true,
        message: `搜索成功，找到${resultCount}条结果`,
        data: response.result
      };
    } else {
      return {
        success: false,
        message: `搜索失败: ${response?.message || '未知错误'}`,
        data: response
      };
    }
  } catch (error) {
    console.error('❌ 搜索失败:', error);
    return {
      success: false,
      message: `搜索失败: ${error.message || '未知错误'}`,
      error: error
    };
  }
};

/**
 * 检查数据格式
 */
export const checkDataFormat = async (): Promise<DebugResult> => {
  try {
    console.log('🔍 开始检查数据格式...');
    
    const response = await getCompanyAutoComplete('', 5);
    
    if (!response) {
      return {
        success: false,
        message: '响应为空'
      };
    }

    // 检查响应结构
    const checks = [
      { name: 'success字段', condition: typeof response.success === 'boolean' },
      { name: 'result字段', condition: Array.isArray(response.result) },
      { name: 'result不为空', condition: response.result && response.result.length > 0 }
    ];

    const failedChecks = checks.filter(check => !check.condition);
    
    if (failedChecks.length === 0) {
      // 检查第一条数据的结构
      const firstItem = response.result[0];
      const itemChecks = [
        { name: 'id字段', condition: typeof firstItem.id === 'string' },
        { name: 'name字段', condition: typeof firstItem.name === 'string' },
      ];

      const failedItemChecks = itemChecks.filter(check => !check.condition);
      
      if (failedItemChecks.length === 0) {
        return {
          success: true,
          message: '数据格式正确',
          data: {
            sampleData: firstItem,
            totalCount: response.result.length
          }
        };
      } else {
        return {
          success: false,
          message: `数据项格式错误: ${failedItemChecks.map(c => c.name).join(', ')}`,
          data: firstItem
        };
      }
    } else {
      return {
        success: false,
        message: `响应格式错误: ${failedChecks.map(c => c.name).join(', ')}`,
        data: response
      };
    }
  } catch (error) {
    console.error('❌ 数据格式检查失败:', error);
    return {
      success: false,
      message: `数据格式检查失败: ${error.message || '未知错误'}`,
      error: error
    };
  }
};

/**
 * 运行完整诊断
 */
export const runFullDiagnosis = async (): Promise<{
  apiConnection: DebugResult;
  dataFormat: DebugResult;
  searchTest: DebugResult;
  summary: string;
}> => {
  console.log('🚀 开始运行CompanyAutoComplete完整诊断...');
  
  const apiConnection = await testAPIConnection();
  const dataFormat = await checkDataFormat();
  const searchTest = await testSearchFunction('测试');
  
  const successCount = [apiConnection, dataFormat, searchTest].filter(r => r.success).length;
  const summary = `诊断完成: ${successCount}/3 项测试通过`;
  
  console.log(`📊 ${summary}`);
  
  return {
    apiConnection,
    dataFormat,
    searchTest,
    summary
  };
};

/**
 * 获取调试建议
 */
export const getDebugSuggestions = (diagnosis: any): string[] => {
  const suggestions: string[] = [];
  
  if (!diagnosis.apiConnection.success) {
    suggestions.push('1. 检查后端服务是否启动');
    suggestions.push('2. 检查API路径是否正确: /basicinfo/company/autoComplete');
    suggestions.push('3. 检查网络连接和CORS配置');
  }
  
  if (!diagnosis.dataFormat.success) {
    suggestions.push('4. 检查后端返回的数据格式');
    suggestions.push('5. 检查数据库中是否有Company数据');
  }
  
  if (!diagnosis.searchTest.success) {
    suggestions.push('6. 检查搜索逻辑是否正确');
    suggestions.push('7. 检查数据库查询条件');
  }
  
  if (suggestions.length === 0) {
    suggestions.push('✅ 所有测试通过，组件应该正常工作');
    suggestions.push('如果仍有问题，请检查组件的使用方式和数据绑定');
  }
  
  return suggestions;
};
