<template>
  <div class="test-page">
    <a-card title="CompanyAutoComplete组件测试">
      <a-space direction="vertical" style="width: 100%">
        
        <!-- 测试区域1：基础功能 -->
        <a-card size="small" title="基础功能测试">
          <a-form layout="vertical">
            <a-form-item label="选择单位">
              <CompanyAutoComplete
                v-model:value="selectedCompanyId"
                placeholder="请选择单位"
                @select="handleCompanySelect"
                @change="handleCompanyChange"
              />
            </a-form-item>
            <a-form-item label="选中的单位ID">
              <a-input :value="selectedCompanyId" readonly />
            </a-form-item>
            <a-form-item label="选中的单位信息">
              <a-textarea 
                :value="JSON.stringify(selectedCompany, null, 2)" 
                :rows="6" 
                readonly 
              />
            </a-form-item>
          </a-form>
        </a-card>

        <!-- 测试区域2：API调试 -->
        <a-card size="small" title="API调试">
          <a-space>
            <a-input 
              v-model:value="testKeyword" 
              placeholder="输入测试关键词" 
              style="width: 200px"
            />
            <a-button type="primary" @click="testAPI" :loading="apiLoading">
              测试API
            </a-button>
            <a-button @click="clearAPIResult">清空结果</a-button>
          </a-space>
          
          <div style="margin-top: 16px;">
            <h4>API响应结果：</h4>
            <a-textarea 
              :value="apiResult" 
              :rows="10" 
              readonly 
              placeholder="点击'测试API'按钮查看结果"
            />
          </div>
        </a-card>

        <!-- 测试区域3：完整诊断 -->
        <a-card size="small" title="完整诊断">
          <a-space>
            <a-button type="primary" @click="runDiagnosis" :loading="diagnosing">
              运行完整诊断
            </a-button>
            <a-button @click="clearDiagnosisResult">清空结果</a-button>
          </a-space>

          <div v-if="diagnosisResult" style="margin-top: 16px;">
            <h4>诊断结果：</h4>
            <a-descriptions :column="1" bordered size="small">
              <a-descriptions-item label="API连接测试">
                <a-tag :color="diagnosisResult.apiConnection.success ? 'success' : 'error'">
                  {{ diagnosisResult.apiConnection.success ? '通过' : '失败' }}
                </a-tag>
                <span style="margin-left: 8px;">{{ diagnosisResult.apiConnection.message }}</span>
              </a-descriptions-item>
              <a-descriptions-item label="数据格式检查">
                <a-tag :color="diagnosisResult.dataFormat.success ? 'success' : 'error'">
                  {{ diagnosisResult.dataFormat.success ? '通过' : '失败' }}
                </a-tag>
                <span style="margin-left: 8px;">{{ diagnosisResult.dataFormat.message }}</span>
              </a-descriptions-item>
              <a-descriptions-item label="搜索功能测试">
                <a-tag :color="diagnosisResult.searchTest.success ? 'success' : 'error'">
                  {{ diagnosisResult.searchTest.success ? '通过' : '失败' }}
                </a-tag>
                <span style="margin-left: 8px;">{{ diagnosisResult.searchTest.message }}</span>
              </a-descriptions-item>
            </a-descriptions>

            <div style="margin-top: 16px;">
              <h4>建议解决方案：</h4>
              <ul>
                <li v-for="(suggestion, index) in suggestions" :key="index">
                  {{ suggestion }}
                </li>
              </ul>
            </div>
          </div>
        </a-card>

        <!-- 测试区域4：网络状态 -->
        <a-card size="small" title="网络状态检查">
          <a-descriptions :column="2" bordered size="small">
            <a-descriptions-item label="后端服务状态">
              <a-tag :color="backendStatus.color">{{ backendStatus.text }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="API接口">
              /basicinfo/company/autoComplete
            </a-descriptions-item>
            <a-descriptions-item label="最后检查时间">
              {{ lastCheckTime || '未检查' }}
            </a-descriptions-item>
            <a-descriptions-item label="错误信息">
              {{ errorMessage || '无' }}
            </a-descriptions-item>
          </a-descriptions>

          <div style="margin-top: 16px;">
            <a-button type="primary" @click="checkBackendStatus" :loading="checking">
              检查后端状态
            </a-button>
          </div>
        </a-card>

      </a-space>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { message } from 'ant-design-vue';
  import CompanyAutoComplete from '@/components/basicinfo/CompanyAutoComplete.vue';
  import { getCompanyAutoComplete } from '@/api/basicinfo/company';
  import type { CompanyAutoCompleteDTO } from '@/types/basicinfo/company';
  import { runFullDiagnosis, getDebugSuggestions } from '@/utils/companyAutoCompleteDebug';

  // 基础功能测试
  const selectedCompanyId = ref<string>('');
  const selectedCompany = ref<CompanyAutoCompleteDTO | null>(null);

  // API调试
  const testKeyword = ref<string>('');
  const apiResult = ref<string>('');
  const apiLoading = ref<boolean>(false);

  // 网络状态检查
  const checking = ref<boolean>(false);
  const lastCheckTime = ref<string>('');
  const errorMessage = ref<string>('');
  const backendStatus = reactive({
    color: 'default',
    text: '未知'
  });

  // 诊断功能
  const diagnosing = ref<boolean>(false);
  const diagnosisResult = ref<any>(null);
  const suggestions = ref<string[]>([]);

  // 事件处理
  const handleCompanySelect = (value: string, option: CompanyAutoCompleteDTO) => {
    console.log('选中单位:', value, option);
    selectedCompany.value = option;
    message.success(`已选择单位: ${option.name}`);
  };

  const handleCompanyChange = (value: string, option?: CompanyAutoCompleteDTO) => {
    console.log('单位变化:', value, option);
    if (!option) {
      selectedCompany.value = null;
      message.info('已清空单位选择');
    }
  };

  // API测试
  const testAPI = async () => {
    try {
      apiLoading.value = true;
      errorMessage.value = '';
      
      console.log('开始测试API，关键词:', testKeyword.value);
      
      const response = await getCompanyAutoComplete(testKeyword.value || undefined, 50);
      
      console.log('API响应:', response);
      
      apiResult.value = JSON.stringify(response, null, 2);
      
      if (response && response.success) {
        message.success(`API调用成功，返回${response.result?.length || 0}条数据`);
        backendStatus.color = 'success';
        backendStatus.text = '正常';
      } else {
        message.warning('API调用成功但返回失败状态');
        backendStatus.color = 'warning';
        backendStatus.text = '异常';
      }
      
    } catch (error) {
      console.error('API调用失败:', error);
      errorMessage.value = error.message || '未知错误';
      apiResult.value = `错误: ${JSON.stringify(error, null, 2)}`;
      message.error('API调用失败');
      backendStatus.color = 'error';
      backendStatus.text = '失败';
    } finally {
      apiLoading.value = false;
      lastCheckTime.value = new Date().toLocaleString();
    }
  };

  const clearAPIResult = () => {
    apiResult.value = '';
    testKeyword.value = '';
  };

  // 运行完整诊断
  const runDiagnosis = async () => {
    try {
      diagnosing.value = true;
      message.info('开始运行完整诊断...');

      const result = await runFullDiagnosis();
      diagnosisResult.value = result;
      suggestions.value = getDebugSuggestions(result);

      const passedTests = [result.apiConnection, result.dataFormat, result.searchTest]
        .filter(test => test.success).length;

      if (passedTests === 3) {
        message.success('所有测试通过！组件应该正常工作');
      } else {
        message.warning(`${passedTests}/3 项测试通过，请查看建议解决方案`);
      }

    } catch (error) {
      console.error('诊断过程出错:', error);
      message.error('诊断过程出错');
    } finally {
      diagnosing.value = false;
    }
  };

  const clearDiagnosisResult = () => {
    diagnosisResult.value = null;
    suggestions.value = [];
  };

  // 后端状态检查
  const checkBackendStatus = async () => {
    try {
      checking.value = true;
      errorMessage.value = '';
      
      // 尝试调用API检查后端状态
      const response = await getCompanyAutoComplete('', 1);
      
      if (response) {
        backendStatus.color = 'success';
        backendStatus.text = '正常';
        message.success('后端服务正常');
      } else {
        backendStatus.color = 'warning';
        backendStatus.text = '异常';
        message.warning('后端服务响应异常');
      }
      
    } catch (error) {
      console.error('后端状态检查失败:', error);
      errorMessage.value = error.message || '连接失败';
      backendStatus.color = 'error';
      backendStatus.text = '失败';
      message.error('后端服务连接失败');
    } finally {
      checking.value = false;
      lastCheckTime.value = new Date().toLocaleString();
    }
  };
</script>

<style lang="less" scoped>
  .test-page {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
  }
</style>
